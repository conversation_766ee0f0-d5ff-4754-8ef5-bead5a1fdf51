---
title: Dialogue Gen
description: Generate multi-speaker dialogue with advanced text-to-speech capabilities.
---

The Dialogue Gen API allows you to create dynamic conversations with multiple speakers using advanced text-to-speech technology. This powerful feature enables you to generate realistic dialogues, conversations, and multi-voice content with customizable voices, emotions, and speaking styles.

## Multi-Speaker TTS
`POST https://api.geminigen.ai/uapi/v1/tts-multi-speaker`

This endpoint allows you to generate speech with multiple speakers, creating natural-sounding conversations and dialogues. You can customize voices, emotions, speed, and other parameters for each speaker or globally for the entire dialogue.

### Example Request
::code-group
```bash [terminal]
curl -X POST https://api.geminigen.ai/uapi/v1/tts-multi-speaker \
  -H "Content-Type: application/json" \
  -H "x-api-key: <your api key>" \
  -d '{
    "voices": ["OA001", "OA002"],
    "model_name": "dialogue_model",
    "model": "tts-flash",
    "speed": 1,
    "blocks": [
      {
        "input": "Hello, how are you today?"
      },
      {
        "input": "I am doing great, thank you for asking!"
      }
    ],
    "output_format": "mp3",
    "emotion": "friendly",
    "custom_prompt": "Speak naturally as if having a casual conversation",
    "output_channel": "mono",
    "name": "Casual Conversation"
  }'
```

```python [python]
import requests

url = "https://api.geminigen.ai/uapi/v1/tts-multi-speaker"
headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
}
data = {
    "voices": ["OA001", "OA002"],
    "model_name": "dialogue_model",
    "model": "tts-flash",
    "speed": 1,
    "blocks": [
        {
            "input": "Welcome to our podcast!"
        },
        {
            "input": "Thank you for having me, I'm excited to be here."
        }
    ],
    "output_format": "mp3",
    "emotion": "enthusiastic",
    "custom_prompt": "Speak as podcast hosts with energy and engagement",
    "output_channel": "stereo",
    "name": "Podcast Interview"
}

response = requests.post(url, headers=headers, json=data)
print(response.json())
```

```typescript [typescript]
import axios from 'axios';

const url = "https://api.geminigen.ai/uapi/v1/tts-multi-speaker";
const headers = {
    "Content-Type": "application/json",
    "x-api-key": "<your api key>"
};
const data = {
    voices: ["OA001", "OA002", "OA003"],
    model_name: "dialogue_model",
    model: "tts-flash",
    speed: 1.2,
    blocks: [
        {
            input: "Good morning, class. Today we'll learn about AI."
        },
        {
            input: "That sounds fascinating! Can you tell us more?"
        },
        {
            input: "Certainly! AI stands for Artificial Intelligence..."
        }
    ],
    output_format: "wav",
    emotion: "educational",
    custom_prompt: "Speak clearly and professionally as in an educational setting",
    output_channel: "mono",
    name: "Educational Dialogue"
};

axios.post(url, data, { headers })
    .then(response => console.log(response.data))
    .catch(error => console.error(error));
```
::

### Request Attributes

`voices` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

An array of voice IDs to be used for the dialogue. Each voice will be assigned to speakers in the order they appear in the blocks. You can find available voice IDs in the [Voice Library](https://ttsopenai.com/voice-library).

`model_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The name identifier for the specific model configuration to use for dialogue generation.

`model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The TTS model to use for speech generation. Default value is `tts-flash`.

`speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed of the speech generation. Value should be between 0.5 and 4.0. Default value is 1.0.

`blocks` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

An array of dialogue blocks, where each block represents a speaker's turn in the conversation. Each block contains the text input for that speaker.

`blocks[].input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The text content that the speaker will say. Maximum length is 10,000 characters per block.

`output_format` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The audio format for the generated output. Supported formats include `mp3`, `wav`, `flac`, and `ogg`. Default is `mp3`.

`emotion` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The overall emotional tone to apply to the dialogue. Examples include "happy", "sad", "excited", "calm", "professional", "friendly", etc.

`custom_prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

A custom prompt that provides additional context or instructions for how the dialogue should be delivered. This helps guide the emotional expression and speaking style.

`output_channel` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The audio channel configuration for the output. Options are `mono` or `stereo`. Default is `mono`.

`voice` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Alternative single voice ID if you want to use the same voice for all speakers in the dialogue.

`input_file_path` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Optional path to an input file containing the dialogue content. Can be used instead of or in addition to the blocks parameter.

`input` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Alternative single text input if you want to generate a monologue instead of a multi-speaker dialogue.

`name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

A descriptive name for the dialogue generation request. This helps with organization and identification of your generated content.

### Example Response
```json [Response]
{
  "success": true,
  "result": {
    "uuid": "f1a2b3c4-d5e6-7f8g-9h0i-j1k2l3m4n5o6",
    "voices": ["OA001", "OA002"],
    "model_name": "dialogue_model",
    "model": "tts-flash",
    "speed": 1,
    "blocks": [
      {
        "input": "Hello, how are you today?"
      },
      {
        "input": "I am doing great, thank you for asking!"
      }
    ],
    "output_format": "mp3",
    "emotion": "friendly",
    "custom_prompt": "Speak naturally as if having a casual conversation",
    "output_channel": "mono",
    "name": "Casual Conversation",
    "estimated_credit": 120,
    "used_credit": 120,
    "status": 1,
    "status_percentage": 25,
    "error_message": "",
    "created_at": "2024-11-21T14:30:00",
    "updated_at": "2024-11-21T14:30:00"
  }
}
```

### Response Attributes

`success` [boolean]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Indicates whether the request was successful.

`result` [object]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The result object containing details about the dialogue generation request.

`result.uuid` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The unique identifier for the dialogue generation request.

`result.voices` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The array of voice IDs used for the dialogue generation.

`result.model_name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The model name used for the dialogue generation.

`result.model` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The TTS model used for speech generation.

`result.speed` [float]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The speed setting used for the speech generation.

`result.blocks` [array]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The dialogue blocks that were processed for speech generation.

`result.output_format` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The audio format of the generated output.

`result.emotion` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The emotional tone applied to the dialogue.

`result.custom_prompt` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The custom prompt used for guiding the dialogue delivery.

`result.output_channel` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The audio channel configuration of the output.

`result.name` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The descriptive name assigned to the dialogue generation.

`result.estimated_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The estimated number of credits required for the dialogue generation.

`result.used_credit` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The actual number of credits used for the dialogue generation.

`result.status` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The current status of the dialogue generation. Possible values are:

- `1`: Processing
- `2`: Completed
- `3`: Error
- `11`: Reworking
- `12`: Joining Audio
- `13`: Merging Audio
- `14`: Downloading Audio

`result.status_percentage` [integer]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The percentage of completion for the dialogue generation process.

`result.error_message` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

Any error message if the generation encountered issues. Empty string if no errors.

`result.created_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The timestamp when the dialogue generation request was created.

`result.updated_at` [string]{style="color: rgb(var(--color-gray-400) / var(--tw-text-opacity, 1)); font-weight: 300;"}

The timestamp when the dialogue generation request was last updated.

## Use Cases

### Podcast Creation
Generate realistic podcast conversations with multiple hosts and guests, complete with natural speaking patterns and emotional expressions.

### Educational Content
Create interactive learning materials with teacher-student dialogues, explanations, and Q&A sessions.

### Storytelling
Develop engaging narratives with character voices, bringing stories to life with distinct personalities for each speaker.

### Customer Service Training
Generate training scenarios with customer-agent interactions for staff training and simulation purposes.

### Marketing Content
Create compelling promotional dialogues, testimonials, and product demonstrations with multiple speakers.

## Best Practices

### Voice Selection
- **Distinct Voices**: Choose voices that are clearly distinguishable from each other
- **Character Matching**: Select voices that match the personality and role of each speaker
- **Consistency**: Use the same voice for the same character throughout longer dialogues

### Content Structure
- **Natural Flow**: Write dialogue that sounds natural and conversational
- **Clear Turns**: Ensure each speaker's turn is clearly defined in separate blocks
- **Appropriate Length**: Keep individual speaking turns to a reasonable length for natural pacing

### Emotional Context
- **Consistent Emotion**: Use emotions that match the overall tone of your dialogue
- **Custom Prompts**: Provide specific guidance for the speaking style and context
- **Scene Setting**: Include context about the situation or environment in your custom prompt

### Technical Considerations
- **Audio Quality**: Choose appropriate output format and channel configuration for your use case
- **Processing Time**: Longer dialogues with multiple speakers may take more time to process
- **Credit Usage**: Multi-speaker generation typically uses more credits than single-speaker TTS
